Context:  Precious_MarketRisk-PnL-DataFlow_V0.3.docx 

IT Key contacts: @<PERSON> , @<PERSON>rim Menasria 

References
Sample files to be ingested from Orchestrade are at \\Mbclenvmfs02\Orchestrade\reports\UAT under

07072025_PL

10072025_Risk 

07072025_VAR

Above files have been added to sharepoint (Precious metals use case files) too for version control

Input file specs:
File

File specs

07072025_PL
image-20250806-141354.png
This file will come from OT and will have trade and position level, profit/loss data for day, monthtoday, and yeartoday as calculated on 7th July 2025

While from a reporting standpoint, only portfolio level PnL is relevant, but datalake should store trade/position level PnLs. Portfolio level PnL is sum of trade/position level PnL in base currency (USD)

Combination of PositionID+TradeID represents a trade. TradeID is same as positionID for exchange traded products, and is 0 for OTCs

Questions/assumptions: 

All PnL numbers are in base currency and base currency will be USD across all trades

06082025_Risk
image-20250806-155751.png
This file has trade level delta with respect to the underlier as calculated on 6th Aug 2025. Delta represents price sensitivity of the trade corresponding to $1 change in the underlier

The book here maps 1:1 to the book in other files

Current quantity represents the “quantity” of instrument held in a certain trade 

Questions/assumptions:

Book is synonymous to portfolio

07072025_VAR

This file has two tabs: 

Sheet1 (below) has portfolio-level and overall 1-day VAR

image-20250806-152428.png
Sheet 2 (below) has trade level VAR

image-20250806-152734.png
Key points: 

As VAR is not additive, above sheets provide VAR numbers at trade, portfolio and overall precious metals level

10-day VAR will be calculated as  1-day VAR * SQRT (10)

The input files have more attributes than are necessary for the market risk report. We can keep these in bronze layer for now and validate/transform/standardise only the necessary ones

Questions / assumptions:

Will Sheet 1 provide the portfolio level VAR as well as overall VAR?

Adjusted PnL
Refer to the PnL report tab on Draft MBCL Risk Dashboard dd-mmm-yy 20250519.xlsx

image-20250807-132922.png
Product control will provide CSV with the following columns: 

Portfolio (e.g XAU), Valuation_Adjustment_Value, Currency (USD)

For portfolio PnL reporting, on top of the daily, monthly, yearly PnL, adjustment value needs to be applied to get corresponding adjusted PnLs

Assumptions: 

PnL adjustments will all be in USD

Output: PowerBI report:
Reference: Draft MBCL Risk Dashboard dd-mmm-yy 20250519.xlsx

Report is expected to provide daily VAR numbers (1-day VAR, 10-day VAR), delta numbers, PL numbers and 1-day changes for a given date

Report should allow drill-down from overall → portfolio for VAR, delta and PL numbers. Delta and PL are additive whereas for VAR, values specific to portfolio/overall book will have to be used

Report should also provide comparison of portfolio and precious metals' risk numbers against the market risk limits (VAR and delta)

Out of scope:
For V1.0, reference data checks and loads (instrument, product, underlier) are out of scope

Trade-level reporting 

Next steps:
Item

Owner

Due date

Agree with Product control format of “adjusted PnL” data

@Natalia Beta 

 

Finalise operational requirements: 

Data loads: 

drop location

timing

schedule vs notification

@Natalia Beta 

 

Finalise exception scenarios and remediation approach

@Natalia Beta 

 

Sample report against sample input data should be created and agreed

@Natalia Beta 

 

Sign-off requirements

@Daniel Kustrin 

 

 